// Test script to verify deliveryNote field is properly converted from quotation to order

const testQuotationData = {
    _id: 'test-quotation-id',
    status: 'draft',
    code: 'QUO-001',
    partnerId: 'test-partner-id',
    currencyId: 'test-currency-id',
    quotationDate: new Date(),
    items: [
        {
            id: 'item-1',
            productId: 'product-1',
            productCode: 'PROD-001',
            productDefinition: 'Test Product 1',
            quantity: 10,
            unitPrice: 100,
            deliveryNote: 'Test delivery note for item 1',
            total: 1000
        },
        {
            id: 'item-2',
            productId: 'product-2',
            productCode: 'PROD-002',
            productDefinition: 'Test Product 2',
            quantity: 5,
            unitPrice: 200,
            deliveryNote: 'Test delivery note for item 2',
            total: 1000
        }
    ],
    subTotal: 2000,
    grandTotal: 2000
};

console.log('Test Quotation Data:');
console.log(JSON.stringify(testQuotationData, null, 2));

// Simulate the convert operation (similar to convert-quotation.js)
const _ = require('lodash');

const convertedOrder = _.omit(testQuotationData, [
    '_id',
    'status',
    'quotationDate',
    'expiryDate',
    'relatedDocuments',
    'revisionId',
    'revisionName',
    'hasRevisions',
    'createdAt',
    'updatedAt',
    'workflowApprovalStatus'
]);

convertedOrder.status = 'draft';
convertedOrder.code = 'ORD-001';
convertedOrder.reference = testQuotationData.code;
convertedOrder.orderDate = testQuotationData.quotationDate;

console.log('\nConverted Order Data:');
console.log(JSON.stringify(convertedOrder, null, 2));

// Check if deliveryNote fields are preserved in items
console.log('\nDelivery Note Check:');
convertedOrder.items.forEach((item, index) => {
    console.log(`Item ${index + 1}: ${item.productCode} - Delivery Note: "${item.deliveryNote}"`);
});

// Verify that deliveryNote fields exist
const hasDeliveryNotes = convertedOrder.items.every(item => item.deliveryNote !== undefined);
console.log(`\nAll items have deliveryNote field: ${hasDeliveryNotes}`);

if (hasDeliveryNotes) {
    console.log('✅ SUCCESS: deliveryNote fields are properly preserved during conversion');
} else {
    console.log('❌ FAILED: deliveryNote fields are missing after conversion');
}
